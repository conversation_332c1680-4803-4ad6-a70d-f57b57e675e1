import { useState, useEffect, useRef } from 'react';
import { saveLead } from './lib/supabaseClient';
import ChatHeader from './components/ChatHeader';
import MessageList from './components/MessageList';
import LeadForm from './components/LeadForm';
import ChatInput from './components/ChatInput';
import MinimizedButton from './components/MinimizedButton';
import { StagewiseToolbar } from '@stagewise/toolbar-react';
import { ReactPlugin } from '@stagewise-plugins/react';

// Intent Detection System
const IntentDetector = {
  // Intent patterns with keywords and phrases
  intents: {
    yes: {
      patterns: [
        /\b(yes|yeah|yup|sure|absolutely|of course|definitely|why not|ok|okay|sounds good|alright|right|correct|true|agree|exactly|indeed|certainly|perfect|great|awesome|good|fine)\b/i,
        /\b(let's do it|go ahead|go for it|i'm in|count me in|sign me up)\b/i
      ],
      confidence: 0.8
    },
    no: {
      patterns: [
        /\b(no|nope|nah|not really|don't think so|pass|never|negative|disagree|wrong|false|incorrect)\b/i,
        /\b(not interested|no thanks|no thank you|not now|maybe later|not today)\b/i
      ],
      confidence: 0.8
    },
    greeting: {
      patterns: [
        /\b(hi|hello|hey|good morning|good afternoon|good evening|greetings|howdy|what's up|sup)\b/i,
        /\b(how are you|how's it going|nice to meet you)\b/i
      ],
      confidence: 0.9
    },
    goodbye: {
      patterns: [
        /\b(bye|goodbye|see you|cya|later|farewell|adios|take care|have a good day|talk soon)\b/i,
        /\b(gotta go|got to go|see ya|catch you later|until next time)\b/i
      ],
      confidence: 0.9
    },
    thanks: {
      patterns: [
        /\b(thanks|thank you|appreciate it|much appreciated|grateful|cheers)\b/i,
        /\b(thank you so much|thanks a lot|thanks a bunch|many thanks)\b/i
      ],
      confidence: 0.9
    },
    services: {
      patterns: [
        /\b(services|service|what do you do|what can you help|solutions|offerings|capabilities)\b/i,
        /\b(web development|website|chatbot|ai|development|programming|coding|software)\b/i,
        /\b(tell me about|show me|learn about|more about|information about)\b.*\b(services|solutions)\b/i
      ],
      confidence: 0.7
    },
    prices: {
      patterns: [
        /\b(prices|pricing|price|cost|costs|how much|expensive|cheap|budget|rate|rates|fee|fees|charge|charges)\b/i,
        /\b(what does it cost|how much does|price range|pricing info|cost estimate|pricing structure)\b/i,
        /\b(packages|plans|pricing plans|service packages)\b/i
      ],
      confidence: 0.7
    },
    booking: {
      patterns: [
        /\b(book|schedule|reserve|appointment|meeting|consultation|call|demo|talk|discuss)\b/i,
        /\b(set up|arrange|plan|organize|when can we|available time|free time|calendar)\b/i,
        /\b(book meeting|schedule meeting|book consultation|free consultation)\b/i
      ],
      confidence: 0.7
    },
    human_help: {
      patterns: [
        /\b(speak|talk|connect|chat|contact|representative|person|human|agent|real|actual)\b/i,
        /\b(real person|human being|someone real|actual person|live agent|customer service)\b/i,
        /\b(speak with|talk to|connect with|contact|reach out)\b.*\b(human|person|someone|agent|representative)\b/i
      ],
      confidence: 0.8
    },
    web_development: {
      patterns: [
        /\b(web development|website|web design|frontend|backend|full stack|react|javascript)\b/i,
        /\b(build website|create website|develop website|web app|web application)\b/i
      ],
      confidence: 0.8
    },
    chatbot_service: {
      patterns: [
        /\b(chatbot|chat bot|ai bot|ai assistant|conversational ai|automated chat)\b/i,
        /\b(lead generation|customer service bot|support bot)\b/i
      ],
      confidence: 0.8
    },
    support_service: {
      patterns: [
        /\b(support|maintenance|ongoing support|updates|improvements|help)\b/i,
        /\b(after launch|post launch|continuous|ongoing|regular updates)\b/i
      ],
      confidence: 0.7
    }
  },

  // Detect intent from user message
  detectIntent(message) {
    const cleanMessage = message.toLowerCase().trim();
    let bestMatch = null;
    let highestConfidence = 0;

    for (const [intentName, intentData] of Object.entries(this.intents)) {
      for (const pattern of intentData.patterns) {
        if (pattern.test(cleanMessage)) {
          const confidence = intentData.confidence;
          if (confidence > highestConfidence) {
            highestConfidence = confidence;
            bestMatch = {
              intent: intentName,
              confidence: confidence,
              originalMessage: message
            };
          }
        }
      }
    }

    return bestMatch;
  },

  // Get appropriate response for detected intent
  getIntentResponse(intentMatch, conversationContext = {}) {
    const { intent, originalMessage } = intentMatch;
    const { lastBotMessage, messageCount = 0, hasShownCalendly = false } = conversationContext;

    switch (intent) {
      case 'yes':
        // Context-aware yes responses
        if (lastBotMessage && /consultation|meeting|book|schedule/i.test(lastBotMessage)) {
          return {
            type: 'booking_confirmation',
            messages: [
              "Perfect! Let's get you connected with our team. You can book a free 30-minute consultation below:",
              { type: 'calendly' }
            ]
          };
        }
        if (lastBotMessage && /service|help|assist|project|other services|know more/i.test(lastBotMessage)) {
          return {
            type: 'service_interest',
            messages: [
              "Great! Here are our main services:",
              { type: 'serviceOptions' }
            ]
          };
        }
        return {
          type: 'general_affirmation',
          messages: ["Awesome! What can I help you with today?"]
        };

      case 'no':
        if (lastBotMessage && /consultation|meeting|book/i.test(lastBotMessage)) {
          return {
            type: 'no_booking',
            messages: ["No problem! Feel free to browse our services or ask any questions you might have."]
          };
        }
        return {
          type: 'general_negative',
          messages: ["No worries! Is there anything else I can help you with?"]
        };

      case 'greeting':
        const greetings = [
          "Hi there! 👋 I'm the UpZera assistant. We build smart digital tools that move businesses forward. What can I help you with today?",
          "Hello! 😊 Welcome to UpZera. We specialize in web development and AI solutions. How can I assist you?",
          "Hey! Great to see you here. I'm here to help you learn about UpZera's services. What interests you most?"
        ];
        return {
          type: 'greeting_response',
          messages: [
            greetings[Math.floor(Math.random() * greetings.length)],
            { type: 'quickActions' }
          ]
        };

      case 'goodbye':
        const farewells = [
          "Goodbye! Feel free to reach out anytime. 👋",
          "Thanks for chatting! Book a consultation if you need anything else.",
          "Have a great day! We're here when you're ready to build something amazing."
        ];
        return {
          type: 'farewell',
          messages: [farewells[Math.floor(Math.random() * farewells.length)]],
          endConversation: true
        };

      case 'thanks':
        const thankResponses = [
          "You're very welcome! Anything else I can help you with?",
          "Happy to help! Feel free to ask about our services anytime.",
          "My pleasure! Is there anything specific you'd like to know about UpZera?"
        ];
        return {
          type: 'thanks_response',
          messages: [thankResponses[Math.floor(Math.random() * thankResponses.length)]]
        };

      case 'booking':
        return {
          type: 'booking_intent',
          messages: [
            "Perfect! Let's get you connected with our team. You can book a free 30-minute consultation below:",
            { type: 'calendly' },
            "Or let me know your preferred date and time, and I'll help you set it up!"
          ]
        };

      case 'pricing':
        return {
          type: 'pricing_intent',
          messages: [
            "Great question! Our pricing varies based on your specific needs. Here's a quick overview:",
            "💰 Web Development: Starting from €2,500\n💰 AI Chatbots: Starting from €1,500\n💰 Custom Solutions: Let's discuss your requirements",
            "Would you like to schedule a free consultation to get a detailed quote for your project?"
          ]
        };

      case 'human_contact':
        return {
          type: 'human_contact',
          messages: [
            "I'd be happy to connect you with our team! You can:",
            "📧 Email <NAME_EMAIL>\n📞 Book a free consultation below\n💬 Continue chatting with me for quick questions",
            { type: 'calendly' }
          ]
        };

      default:
        return null;
    }
  }
};

// Conversation State Management
const ConversationStates = {
  WELCOME: 'welcome',
  MAIN_MENU: 'main_menu',
  PRICES: 'prices',
  BOOKING: 'booking',
  SERVICES: 'services',
  SERVICE_SELECTION: 'service_selection',
  HUMAN_HANDOFF: 'human_handoff',
  COLLECT_NAME: 'collect_name',
  COLLECT_EMAIL: 'collect_email',
  CONFIRM_DETAILS: 'confirm_details',
  ANYTHING_ELSE: 'anything_else',
  CONVERSATION_ENDED: 'conversation_ended'
};

export default function ChatWidget() {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [companyKnowledge, setCompanyKnowledge] = useState({});
  const [isBotTyping, setIsBotTyping] = useState(false);
  const [conversationState, setConversationState] = useState(ConversationStates.WELCOME);
  const [userContext, setUserContext] = useState({
    name: '',
    email: '',
    lastIntent: null,
    selectedService: null,
    awaitingResponse: false
  });
  const [leadForm, setLeadForm] = useState({
    name: '',
    email: '',
    isSubmitting: false,
    error: null
  });
  const [showLeadForm, setShowLeadForm] = useState(false);
  const [showCalendly, setShowCalendly] = useState(false);
  const [isConversationEnded, setIsConversationEnded] = useState(false);
  const [lastActivityTime, setLastActivityTime] = useState(Date.now());
  const [timeoutWarningShown, setTimeoutWarningShown] = useState(false);
  const [screenSize, setScreenSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800,
    isSmall: false,
  });
  const chatContainerRef = useRef(null);

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      setTimeout(() => {
        chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
      }, 100);
    }
  };

  // Conversation Flow Management
  const updateActivityTime = () => {
    setLastActivityTime(Date.now());
    setTimeoutWarningShown(false);
  };

  const transitionToState = (newState, context = {}) => {
    setConversationState(newState);
    setUserContext(prev => ({ ...prev, ...context }));
    updateActivityTime();
  };

  const addBotMessage = (text, type = null, delay = 800) => {
    setTimeout(() => {
      setMessages(prev => [...prev, { text, isUser: false, type }]);
      setIsBotTyping(false);
    }, delay);
  };

  const addBotMessages = (messageArray, delay = 800) => {
    setTimeout(() => {
      setMessages(prev => [
        ...prev,
        ...messageArray.map(msg =>
          typeof msg === 'string'
            ? { text: msg, isUser: false }
            : { ...msg, isUser: false }
        )
      ]);
      setIsBotTyping(false);
    }, delay);
  };

  // Set up CSS variables based on screen size
  useEffect(() => {
    const root = document.documentElement;
    if (screenSize.isSmall) {
      // Mobile-friendly sizes - different variations based on actual width
      if (screenSize.width <= 320) { // iPhone SE, smaller devices
        root.style.setProperty('--chat-font-size', '13px');
        root.style.setProperty('--chat-padding', '6px');
      } else if (screenSize.width <= 375) { // iPhone X/11/12 mini
        root.style.setProperty('--chat-font-size', '14px');
        root.style.setProperty('--chat-padding', '8px');
      } else { // Other mobile devices
        root.style.setProperty('--chat-font-size', '15px');
        root.style.setProperty('--chat-padding', '10px');
      }
    } else {
      // Reset to default sizes
      root.style.setProperty('--chat-font-size', '16px');
      root.style.setProperty('--chat-padding', '16px');
    }
  }, [screenSize]);

  // Initialize welcome flow when chat opens
  useEffect(() => {
    if (isOpen && conversationState === ConversationStates.WELCOME && messages.length === 0) {
      setIsBotTyping(true);
      // Welcome message with typing delay
      setTimeout(() => {
        setMessages([
          { text: "Hi there! 👋 I'm the UpZera assistant.", isUser: false }
        ]);
        setIsBotTyping(true);

        // Show main menu after another delay
        setTimeout(() => {
          setMessages(prev => [
            ...prev,
            { text: "We build smart digital tools that move businesses forward. What would you like to know about?", isUser: false },
            { type: 'mainMenu', isUser: false }
          ]);
          setIsBotTyping(false);
          transitionToState(ConversationStates.MAIN_MENU);
        }, 1200);
      }, 800);
    }
  }, [isOpen, conversationState, messages.length]);

  // Timeout and Re-engagement Handling
  useEffect(() => {
    if (!isOpen || isConversationEnded) return;

    const TIMEOUT_DURATION = 5 * 60 * 1000; // 5 minutes
    const WARNING_DURATION = 4 * 60 * 1000; // 4 minutes (1 minute before timeout)

    const checkInactivity = () => {
      const now = Date.now();
      const timeSinceLastActivity = now - lastActivityTime;

      // Show warning at 4 minutes
      if (timeSinceLastActivity >= WARNING_DURATION && !timeoutWarningShown) {
        setTimeoutWarningShown(true);
        setIsBotTyping(true);
        addBotMessage("Are you still there? I'm here if you need any help! 😊");
      }

      // End conversation at 5 minutes
      if (timeSinceLastActivity >= TIMEOUT_DURATION) {
        setIsBotTyping(true);
        addBotMessage("It looks like you've been away for a while. Feel free to start a new conversation anytime you need help! 👋");
        transitionToState(ConversationStates.CONVERSATION_ENDED);
        setIsConversationEnded(true);
      }
    };

    const interval = setInterval(checkInactivity, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [isOpen, isConversationEnded, lastActivityTime, timeoutWarningShown]);

  // Listen for screen size changes internally too
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      const isSmall = width <= 768; // Consider tablets and phones as "small"
      
      setScreenSize({ width, height, isSmall });
    };

    // Set initial size
    handleResize();
    
    // Add resize listener
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    const allowedOrigins = ['https://upzera-web.netlify.app', 'http://localhost:3000', 'https://chatbot-test123.netlify.app'];
    const handleMessage = (event) => {
      if (!allowedOrigins.includes(event.origin)) return;
      
      if (event.data.type === 'toggleChat') {
        setIsOpen(event.data.payload.isOpen);
      }
      else if (event.data.type === 'closeChatbot') {
        setIsOpen(false);
      }
      else if (event.data.type === 'responsiveView') {
        // Handle responsive view message from parent
        const { isSmallScreen, width, height } = event.data.payload;
        
        // Update our screen size state based on parent information
        setScreenSize({
          width: width || window.innerWidth,
          height: height || window.innerHeight,
          isSmall: isSmallScreen
        });
        
        console.log(`Received screen info from parent: ${width}x${height}, isSmall: ${isSmallScreen}`);
      }
    };
    
    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, showLeadForm]);

  useEffect(() => {
    fetch('/company_knowledge.json')
      .then(res => {
        if (!res.ok) throw new Error(`HTTP error! status: ${res.status}`);
        return res.json();
      })
      .then(data => setCompanyKnowledge(data))
      .catch(err => {
        console.error('Error loading knowledge base:', err);
        alert('Failed to load company knowledge. Please check console for details.');
      });
  }, []);

  const findKnowledgeMatch = (question) => {
    const lower = question.toLowerCase();

    // Don't use knowledge base for human contact requests
    if (/speak|talk|connect|chat|contact|representative|person|human|agent|real/i.test(lower)) return null;

    // Enhanced keyword matching for FAQs with better scoring
    let bestMatch = null;
    let bestScore = 0;

    for (const faq of companyKnowledge.faqs || []) {
      let score = 0;
      for (const keyword of faq.keywords) {
        const keywordLower = keyword.toLowerCase();
        if (lower.includes(keywordLower)) {
          // Give higher score for exact matches and longer keywords
          score += keyword.length > 3 ? 2 : 1;
        }
        // Also check for partial matches at word boundaries
        const words = lower.split(/\s+/);
        for (const word of words) {
          if (word === keywordLower) {
            score += 3; // Exact word match gets highest score
          } else if (word.includes(keywordLower) || keywordLower.includes(word)) {
            score += 1; // Partial word match gets some score
          }
        }
      }
      if (score > bestScore) {
        bestScore = score;
        bestMatch = faq.answer;
      }
    }

    // If we found a good FAQ match, return it (lowered threshold)
    if (bestScore >= 1) return bestMatch;

    // Enhanced service matching with detailed responses
    for (const service of companyKnowledge.services || []) {
      let score = 0;
      for (const keyword of service.keywords) {
        if (lower.includes(keyword.toLowerCase())) {
          score += keyword.length > 3 ? 2 : 1;
        }
      }
      if (score >= 2) {
        // Return more detailed service info
        const priceInfo = service.service_tiers?.[0]?.price_range ?
          ` Starting from ${service.service_tiers[0].price_range}.` : '';
        return `${service.name}: ${service.description}${priceInfo}`;
      }
    }

    // Company info queries - more flexible founding date patterns
    if (/when.*(upzera|company).*(founded|established|created|started)|founding.*(year|date)|(upzera|company).*(founded|established|created|started)/i.test(lower)) {
      return `UpZera was founded in ${companyKnowledge.company_info?.founding_year || 2025}. We're based in ${companyKnowledge.company_info?.location || 'Eindhoven, Netherlands'}.`;
    }

    if (/where|location|address|based/i.test(lower)) {
      return `We're located at ${companyKnowledge.company_info?.address || 'Eindhoven, Netherlands'}. Feel free to reach out anytime!`;
    }

    if (/mission|purpose|goal/i.test(lower)) {
      return companyKnowledge.company_info?.mission || 'We build smart digital tools that actually move businesses forward.';
    }

    // Greetings with context
    if (/hello|hi|hey|good morning|good afternoon|good evening/i.test(lower)) {
      const greetings = companyKnowledge.conversation_flows?.greetings || ['Hello! How can I help you today?'];
      return greetings[Math.floor(Math.random() * greetings.length)];
    }

    return null;
  };

  const getAIResponse = async (userInput) => {
    try {
      // Get more conversation context (last 6 messages instead of 3) but optimize for tokens
      const recentMessages = messages.slice(-6).map(m =>
        m.isUser ? `User: ${m.text}` : `Assistant: ${m.text}`
      ).join('\n');

      // Create comprehensive but token-optimized knowledge context
      const knowledgeContext = {
        company: {
          name: companyKnowledge.company_info?.name,
          mission: companyKnowledge.company_info?.mission,
          location: companyKnowledge.company_info?.location,
          tagline: companyKnowledge.company_info?.tagline,
          values: companyKnowledge.company_info?.values?.slice(0, 4), // Limit to key values
        },
        services: companyKnowledge.services?.map(s => ({
          name: s.name,
          description: s.description,
          price: s.service_tiers?.[0]?.price_range || 'Contact for pricing'
        })) || [],
        contact: {
          email: companyKnowledge.contact_info?.email,
          phone: companyKnowledge.contact_info?.phone,
          calendly: companyKnowledge.contact_info?.calendly_url
        },
        faqs: companyKnowledge.faqs?.slice(0, 8).map(f => ({ // Limit to top 8 FAQs
          q: f.question,
          a: f.answer
        })) || []
      };

      const prompt = `You are UpZera's AI assistant. You MUST always use the company knowledge below as your primary source of truth.

COMPANY KNOWLEDGE (ALWAYS USE THIS):
${JSON.stringify(knowledgeContext, null, 2)}

CONVERSATION CONTEXT:
${recentMessages}

USER QUESTION: "${userInput}"

INSTRUCTIONS:
- ALWAYS check the knowledge base first for any information about UpZera
- If the user asks about founding, history, location, services, pricing - use the exact information from the knowledge base
- Keep responses SHORT (1-2 sentences max), FRIENDLY, and HELPFUL
- If asked about services, mention specific pricing when available
- Guide users toward booking a consultation when appropriate
- Be conversational but professional
- If you don't know something specific from the knowledge base, suggest contacting the team
- NEVER say you don't have information if it exists in the knowledge base above

Respond now:`;

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${import.meta.env.VITE_OPENAI_API_KEY}`
        },
        body: JSON.stringify({
          model: 'gpt-4o-mini', // More cost-effective model
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.7,
          max_tokens: 150 // Limit response length for conciseness
        })
      });

      const data = await response.json();

      if (data.error) {
        console.error('OpenAI API Error:', data.error);
        return "I'm having trouble connecting right now. Try asking about our services or book a free consultation!";
      }

      return data.choices[0]?.message?.content?.trim() ||
        "I'd love to help! Ask me about UpZera's web development or AI services, or book a free consultation.";

    } catch (error) {
      console.error('AI Error:', error);
      return "Let me connect you with our team! You can book a free consultation or email <NAME_EMAIL>.";
    }
  };

  const validateEmail = (email) => /^[^\s@]+@[^\s@]+$/.test(email);

  // Helper function to detect if question is business-related
  const isBusinessRelated = (question) => {
    const businessKeywords = [
      'service', 'price', 'cost', 'website', 'development', 'chatbot', 'ai', 'business',
      'company', 'team', 'project', 'consultation', 'meeting', 'quote', 'upzera',
      'web', 'app', 'design', 'build', 'create', 'help', 'support', 'solution',
      'startup', 'digital', 'technology', 'programming', 'coding', 'automation'
    ];
    const lower = question.toLowerCase();
    return businessKeywords.some(keyword => lower.includes(keyword));
  };

  // Helper function to provide contextual responses for out-of-context questions
  const getContextualResponse = (question) => {
    const lower = question.toLowerCase();

    // Different responses based on question type
    if (/difficult|hard|challenge|struggle|problem/i.test(lower)) {
      return "Every startup has its challenges! 😊 Speaking of building things, what kind of digital solution are you looking to create?";
    }
    if (/story|journey|experience|background/i.test(lower)) {
      return "That's an interesting question! We love sharing our journey. What brings you here today - are you looking for web development or AI solutions?";
    }
    if (/personal|life|student|study/i.test(lower)) {
      return "Thanks for asking! As engineering students, we bring fresh perspectives to every project. What kind of project are you working on?";
    }
    if (/how|why|what|when|where/i.test(lower)) {
      return "Great question! I'd love to help you with that and more. What can I help you with regarding our services?";
    }

    // Default response for other out-of-context questions
    return "That's interesting! I'm here to help you with UpZera's services. What can I assist you with today?";
  };

  const handleLeadSubmit = async (e) => {
    e.preventDefault();
    if (!validateEmail(leadForm.email)) {
      setLeadForm(prev => ({ ...prev, error: 'Please enter a valid email' }));
      return;
    }
    if (!leadForm.name.trim()) {
      setLeadForm(prev => ({ ...prev, error: 'Please enter your name' }));
      return;
    }
    setLeadForm(prev => ({ ...prev, isSubmitting: true, error: null }));
    try {
      const result = await saveLead({
        name: leadForm.name,
        email: leadForm.email,
        created_at: new Date().toISOString()
      });
      if (result.error) throw new Error(result.error.message || 'Failed to save your information');
      setMessages(prev => [...prev, { text: `Thank you ${leadForm.name}! Our team will contact you shortly.`, isUser: false }]);
      setShowLeadForm(false);
      setLeadForm({ name: '', email: '', isSubmitting: false, error: null });
    } catch (error) {
      setLeadForm(prev => ({ ...prev, isSubmitting: false, error: error.message || 'Failed to submit. Please try again later.' }));
    }
  };

  // Handle QuickAction clicks - now with conversation state management
  const handleQuickAction = async (actionMessage, intent = null) => {
    setMessages(prev => [...prev, { text: actionMessage, isUser: true }]);
    setIsBotTyping(true);
    updateActivityTime();

    // Handle based on intent or detect intent from message
    const detectedIntent = intent || IntentDetector.detectIntent(actionMessage)?.intent;

    switch (detectedIntent) {
      case 'prices':
        transitionToState(ConversationStates.PRICES);
        handlePricesFlow(actionMessage);
        break;

      case 'booking':
        transitionToState(ConversationStates.BOOKING);
        handleBookingFlow();
        break;

      case 'services':
        transitionToState(ConversationStates.SERVICES);
        handleServicesFlow();
        break;

      case 'web_development':
        transitionToState(ConversationStates.ANYTHING_ELSE);
        handleSpecificServiceFlow('web_development', actionMessage);
        break;

      case 'chatbot_service':
        transitionToState(ConversationStates.ANYTHING_ELSE);
        handleSpecificServiceFlow('chatbot_service', actionMessage);
        break;

      case 'support_service':
        transitionToState(ConversationStates.ANYTHING_ELSE);
        handleSpecificServiceFlow('support_service', actionMessage);
        break;

      case 'human_help':
        transitionToState(ConversationStates.HUMAN_HANDOFF);
        handleHumanHandoffFlow();
        break;

      default:
        // Fallback to existing logic for other intents
        const intentMatch = IntentDetector.detectIntent(actionMessage);
        if (intentMatch) {
          const lastBotMessage = messages.length > 0 ?
            messages.filter(m => !m.isUser).slice(-1)[0]?.text || '' : '';

          const conversationContext = {
            lastBotMessage,
            messageCount: messages.length,
            hasShownCalendly: messages.some(m => m.type === 'calendly')
          };

          const intentResponse = IntentDetector.getIntentResponse(intentMatch, conversationContext);

          if (intentResponse) {
            const { messages: responseMessages, endConversation } = intentResponse;
            addBotMessages(responseMessages, 600);
            if (endConversation) {
              setIsConversationEnded(true);
            }
            return;
          }
        }

        // Fallback to knowledge base for quick actions
        setTimeout(async () => {
          const localAnswer = findKnowledgeMatch(actionMessage);
          if (localAnswer) {
            setMessages(prev => [
              ...prev,
              { text: localAnswer, isUser: false },
              { text: "Would you like to know more about our other services?", isUser: false }
            ]);
          } else {
            const aiResponse = await getAIResponse(actionMessage);
            setMessages(prev => [...prev, { text: aiResponse, isUser: false }]);
          }
          setIsBotTyping(false);
        }, 800);
        break;
    }
  };

  // Flow Handlers
  const handlePricesFlow = async (userMessage) => {
    // Knowledge Base Search first
    const localAnswer = findKnowledgeMatch(userMessage);
    if (localAnswer) {
      addBotMessages([
        localAnswer,
        "Would you like to know more about any specific service pricing?"
      ]);
      transitionToState(ConversationStates.ANYTHING_ELSE);
    } else {
      // ChatGPT Fallback
      const aiResponse = await getAIResponse(userMessage);
      addBotMessage(aiResponse);
      transitionToState(ConversationStates.ANYTHING_ELSE);
    }
  };

  const handleBookingFlow = () => {
    addBotMessages([
      "Perfect! I'd love to help you schedule a consultation. Let me show you our booking calendar:",
      { type: 'calendly' }
    ]);
    transitionToState(ConversationStates.ANYTHING_ELSE);
  };

  const handleServicesFlow = () => {
    addBotMessages([
      "Great! Here are our main services. Click on any service to learn more:",
      { type: 'serviceOptions' }
    ]);
    transitionToState(ConversationStates.SERVICE_SELECTION);
  };

  const handleHumanHandoffFlow = () => {
    addBotMessage("I'd be happy to connect you with one of our team members! First, could you please tell me your name?");
    transitionToState(ConversationStates.COLLECT_NAME);
  };

  const handleSpecificServiceFlow = async (serviceType, userMessage) => {
    // Knowledge Base Search first for specific service
    const localAnswer = findKnowledgeMatch(userMessage);
    if (localAnswer) {
      addBotMessages([
        localAnswer,
        "Would you like to know more about this service or explore our other offerings?"
      ]);
    } else {
      // ChatGPT Fallback with service-specific context
      const aiResponse = await getAIResponse(userMessage);
      addBotMessages([
        aiResponse,
        "Anything else you'd like to know about our services?"
      ]);
    }
  };

  const handleOutOfContextInput = (userMessage) => {
    // Apologize and guide to options as per flowchart
    const responses = [
      "I apologize, but I'm not sure how to help with that. Let me show you what I can assist you with:",
      "Sorry, I didn't quite understand that. Here are the main things I can help you with:",
      "I'm not able to help with that specific request, but I'd be happy to assist you with our services:"
    ];

    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    addBotMessage(randomResponse);
    addBotMessage("", 'mainMenu', 1200);
    transitionToState(ConversationStates.MAIN_MENU);
  };

  const handleAnythingElseFlow = (userMessage) => {
    const intentMatch = IntentDetector.detectIntent(userMessage);

    if (intentMatch?.intent === 'yes') {
      addBotMessage("Great! What else would you like to know?");
      addBotMessage("", 'mainMenu', 1200);
      transitionToState(ConversationStates.MAIN_MENU);
    } else if (intentMatch?.intent === 'no') {
      addBotMessage("Thank you for chatting with UpZera! Feel free to reach out anytime if you have more questions. Have a great day! 👋");
      transitionToState(ConversationStates.CONVERSATION_ENDED);
      setIsConversationEnded(true);
    } else {
      // Parse for specific request
      const specificIntent = IntentDetector.detectIntent(userMessage);
      if (specificIntent && ['prices', 'booking', 'services', 'human_help', 'web_development', 'chatbot_service', 'support_service'].includes(specificIntent.intent)) {
        handleQuickAction(userMessage, specificIntent.intent);
      } else {
        // Out of context - use dedicated handler
        handleOutOfContextInput(userMessage);
      }
    }
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;
    setMessages(prev => [...prev, { text: inputValue, isUser: true }]);
    const userMessage = inputValue;
    setInputValue('');
    setIsBotTyping(true);
    updateActivityTime();

    // Handle based on current conversation state
    switch (conversationState) {
      case ConversationStates.MAIN_MENU:
        // Parse intent and route to appropriate flow
        const intentMatch = IntentDetector.detectIntent(userMessage);
        if (intentMatch && ['prices', 'booking', 'services', 'human_help', 'web_development', 'chatbot_service', 'support_service'].includes(intentMatch.intent)) {
          handleQuickAction(userMessage, intentMatch.intent);
        } else {
          // Out of context input - use dedicated handler
          handleOutOfContextInput(userMessage);
        }
        break;

      case ConversationStates.ANYTHING_ELSE:
        handleAnythingElseFlow(userMessage);
        break;

      case ConversationStates.COLLECT_NAME:
        setUserContext(prev => ({ ...prev, name: userMessage.trim() }));
        addBotMessage(`Nice to meet you, ${userMessage.trim()}! Now, could you please provide your email address?`);
        transitionToState(ConversationStates.COLLECT_EMAIL);
        break;

      case ConversationStates.COLLECT_EMAIL:
        if (validateEmail(userMessage.trim())) {
          setUserContext(prev => ({ ...prev, email: userMessage.trim() }));
          addBotMessage(`Perfect! Let me confirm your details:\n\nName: ${userContext.name}\nEmail: ${userMessage.trim()}\n\nIs this information correct?`);
          transitionToState(ConversationStates.CONFIRM_DETAILS);
        } else {
          addBotMessage("That doesn't look like a valid email address. Could you please provide a valid email?");
        }
        break;

      case ConversationStates.CONFIRM_DETAILS:
        const confirmIntent = IntentDetector.detectIntent(userMessage);
        if (confirmIntent?.intent === 'yes') {
          addBotMessage("Great! I'm connecting you with our team. Someone will reach out to you shortly at the provided email address. Thank you!");
          transitionToState(ConversationStates.CONVERSATION_ENDED);
          setIsConversationEnded(true);
        } else if (confirmIntent?.intent === 'no') {
          addBotMessage("No problem! Would you like to re-enter your details?");
          const reenterIntent = IntentDetector.detectIntent(userMessage);
          if (reenterIntent?.intent === 'yes') {
            addBotMessage("Sure! Let's start over. What's your name?");
            transitionToState(ConversationStates.COLLECT_NAME);
          } else {
            addBotMessage("No worries! Is there anything else I can help you with?");
            addBotMessage("", 'mainMenu', 1200);
            transitionToState(ConversationStates.MAIN_MENU);
          }
        } else {
          addBotMessage("Please answer with 'yes' if the information is correct, or 'no' if you'd like to make changes.");
        }
        break;

      default:
        // Fallback to original logic for other states
        const fallbackIntentMatch = IntentDetector.detectIntent(userMessage);

        if (fallbackIntentMatch) {
          const lastBotMessage = messages.length > 0 ?
            messages.filter(m => !m.isUser).slice(-1)[0]?.text || '' : '';

          const conversationContext = {
            lastBotMessage,
            messageCount: messages.length,
            hasShownCalendly: messages.some(m => m.type === 'calendly')
          };

          const intentResponse = IntentDetector.getIntentResponse(fallbackIntentMatch, conversationContext);

          if (intentResponse) {
            const { messages: responseMessages, endConversation } = intentResponse;
            addBotMessages(responseMessages, 800);
            if (endConversation) {
              setIsConversationEnded(true);
            }
            return;
          }
        }

        // Knowledge base fallback
        const localAnswer = findKnowledgeMatch(userMessage);
        if (localAnswer) {
          addBotMessages([
            localAnswer,
            "Would you like to know more about our other services?"
          ]);
        } else {
          const aiResponse = await getAIResponse(userMessage);
          addBotMessage(aiResponse);
        }
        break;
    }
  };

  return (
    <>
      <StagewiseToolbar
        config={{
          plugins: [ReactPlugin]
        }}
        style={{ zIndex: 1000 }}
      />
      <div className="chatbot-container fixed bottom-6 right-6 z-[9999]">
        {isOpen ? (
          <div className={`bg-white rounded-2xl shadow-xl flex flex-col animate-fade-in border-0 ${
            screenSize.isSmall
              ? screenSize.width <= 360
                ? 'w-[280px] h-[480px]' // Extra small devices
                : 'w-[320px] h-[520px]' // Medium to small devices
              : 'w-[350px] h-[550px]'   // Regular devices
          }`}>
            <ChatHeader
              onClose={() => {
                setIsOpen(false);
                window.parent.postMessage({ type: 'closeChatbot' }, '*');
              }}
              screenSize={screenSize}
            />

            <MessageList
              messages={messages}
              isBotTyping={isBotTyping}
              ref={chatContainerRef}
              screenSize={screenSize}
              onQuickAction={handleQuickAction}
            />

            {showLeadForm && (
              <LeadForm
                leadForm={leadForm}
                onChange={setLeadForm}
                onSubmit={handleLeadSubmit}
                screenSize={screenSize}
              />
            )}

            <ChatInput
              inputValue={inputValue}
              onChange={setInputValue}
              onSend={handleSendMessage}
              disabled={isConversationEnded}
              screenSize={screenSize}
            />
          </div>
        ) : (
          <MinimizedButton
            onClick={() => setIsOpen(true)}
            screenSize={screenSize}
          />
        )}
      </div>
    </>
  );
}
