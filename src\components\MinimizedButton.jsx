import React from 'react';

export default function MinimizedButton({ onClick, screenSize }) {
  // Get screen size properties
  const isSmall = screenSize?.isSmall || false;
  const width = screenSize?.width || 1200;
  
  // Adjust button size based on screen size
  let buttonSize = 'w-12 h-12'; // Default size
  let iconSize = 'w-7 h-7';     // Default icon size

  if (isSmall) {
    if (width <= 320) {
      buttonSize = 'w-9 h-9';    // Extra small screens
      iconSize = 'w-5 h-5';
    } else if (width <= 375) {
      buttonSize = 'w-10 h-10';  // Small screens (iPhone X/11 size)
      iconSize = 'w-6 h-6';
    } else {
      buttonSize = 'w-11 h-11';  // Medium screens
      iconSize = 'w-6 h-6';
    }
  }

  return (
    <button
      onClick={onClick}
      className={`bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 
                 rounded-full shadow-lg transition-all flex items-center justify-center ${buttonSize}`}
      aria-label="Open chat"
    >
      <svg xmlns="http://www.w3.org/2000/svg" className={`${iconSize} text-white`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
      </svg>
    </button>
  );
}