flowchart TD
    A["Chat Opened"] --> B["Welcome + Typing Delay"]
    B --> C["Show Main Menu Options:<br/>1. PRICES<br/>2. BOOK MEETING<br/>3. SERVICES"]
    
    C --> D["User Selects PRICES"]
    C --> E["User Selects BOOK MEETING"]
    C --> F["User Selects SERVICES"]
    C --> G["Out of Context Input"]
    
    D --> D1["Knowledge Base Search"]
    D1 --> D2["KB Found Answer?"]
    D2 -->|Yes| D3["Provide Price Info"]
    D2 -->|No| D4["ChatGPT Fallback"]
    D4 --> D3
    D3 --> H["Ask: Anything else?"]
    
    E --> E1["Show Booking Integration"]
    E1 --> H
    
    F --> F1["Show Service Options"]
    F1 --> F2["User Selects Service"]
    F2 --> F3["Knowledge Base Search"]
    F3 --> F4["KB Found Answer?"]
    F4 -->|Yes| F5["Provide Service Info"]
    F4 -->|No| F6["ChatGPT Fallback"]
    F6 --> F5
    F5 --> H
    
    G --> G1["Apologize + Guide to Options"]
    G1 --> C
    
    H --> I["User Response"]
    I --> J["Response Type?"]
    J -->|"Yes" Intent| C
    J -->|"No" Intent| K["End Conversation"]
    J -->|Specific Request| L["Parse Intent"]
    J -->|Out of Context| G
    
    L --> L1["Intent Type?"]
    L1 -->|Prices| D
    L1 -->|Booking| E
    L1 -->|Services| F
    L1 -->|Human Help| M
    L1 -->|Unknown| G
    
    %% Human Handoff (Can happen from ANY state)
    M["Human Handoff Request"]
    M --> M1["Ask for Name"]
    M1 --> M2["Ask for Email"]
    M2 --> M3["Validate Email Format"]
    M3 -->|Invalid| M4["Ask for Valid Email"]
    M4 --> M2
    M3 -->|Valid| M5["Confirm Details"]
    M5 --> M6["User Confirms?"]
    M6 -->|Yes| M7["Initiate Human Handoff"]
    M6 -->|No| M8["Ask to Re-enter Details?"]
    M8 -->|Yes| M1
    M8 -->|No| H
    M7 --> M9["Handoff Successful"]
    M9 --> K
    
    %% Timeout Handling
    N["User Inactive (5+ min)"] --> N1["Send Re-engagement Message"]
    N1 --> N2["Still No Response?"]
    N2 -->|Response| I
    N2 -->|No Response| K
    
    %% Connect Human Handoff to all states
    D -.-> M
    E -.-> M
    E1 -.-> M
    F -.-> M
    C -.-> M
    H -.-> M
    I -.-> M
    
    %% Connect Timeout to waiting states
    C -.-> N
    H -.-> N
    I -.-> N
    
    %% Style the nodes
    classDef startEnd fill:#e1f5fe
    classDef process fill:#f3e5f5
    classDef decision fill:#fff3e0
    classDef error fill:#ffebee
    classDef human fill:#e8f5e8
    
    class A,K startEnd
    class B,C,D3,E3,F5,G1,M7,M9 process
    class D2,E2,F4,J,L1,M3,M6,M8,N2 decision
    class G,G1,M4 error
    class M,M1,M2,M5,M7,M9 human